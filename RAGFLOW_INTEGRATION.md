# RAGFlow Integration Guide

This document explains how to use the RAGFlow chatbot engine integration in the jurbot application.

## Overview

The jurbot application now supports two chatbot engines:
- **n8n Workflow** (existing): Uses n8n webhooks for AI responses
- **RAGFlow** (new): Uses RAG<PERSON>low's API for AI responses with document retrieval capabilities

## Features

### RAGFlow Integration Features
- ✅ **Dual Engine Support**: Switch between n8n and RAGFlow engines
- ✅ **Admin Configuration**: Easy setup through the settings UI
- ✅ **Real-time Status**: Visual indicator showing active engine and configuration status
- ✅ **Error Handling**: Comprehensive error handling and fallback mechanisms
- ✅ **Streaming Support**: Real-time streaming responses (foundation implemented)
- ✅ **Configuration Validation**: Automatic validation of engine configurations
- ✅ **Local Storage**: Configuration persisted locally in browser

## Configuration

### Accessing Settings
1. Click the **Settings** button in the sidebar (gear icon)
2. The "Chatbot Engine Configuration" dialog will open

### RAGFlow Configuration
To configure RAGFlow as your chatbot engine:

1. **Select Engine**: Choose "RAGFlow" from the engine selector
2. **API Key**: Enter your RAGFlow API key
3. **Base URL**: Enter your RAGFlow instance URL (default: `http://193.30.121.199`)
4. **Chat ID**: Enter the shared chat ID (default: `2291b3525cfc11f0990e5a471b8c9178`)
5. **Test Connection**: Click "Test Connection" to verify the configuration
6. **Save**: Click "Save Configuration" to apply the settings

### n8n Configuration
To configure n8n as your chatbot engine:

1. **Select Engine**: Choose "n8n Workflow" from the engine selector
2. **Webhook URL**: Enter your n8n webhook URL
3. **Test Connection**: Click "Test Connection" to verify the configuration
4. **Save**: Click "Save Configuration" to apply the settings

## Usage

### Engine Status Indicator
The chat interface displays the current engine status in the header:
- **Green indicator**: Engine is properly configured and ready
- **Orange indicator**: Engine is selected but not properly configured
- **Engine icon**: 
  - ⚡ (Zap) for n8n
  - 🤖 (Bot) for RAGFlow

### Switching Engines
1. Open the settings dialog
2. Select the desired engine
3. Configure the engine settings
4. Test the connection
5. Save the configuration

The change takes effect immediately for new messages.

## API Endpoints

### RAGFlow Test Endpoint
```
POST /api/test-ragflow
Content-Type: application/json

{
  "apiKey": "your-api-key",
  "baseUrl": "http://193.30.121.199",
  "chatId": "2291b3525cfc11f0990e5a471b8c9178"
}
```

**Note**: This uses RAGFlow's OpenAI-compatible API endpoint:
`/api/v1/chats_openai/{chat_id}/chat/completions`

### RAGFlow Streaming Endpoint (Future Enhancement)
```
POST /api/ragflow-stream
Content-Type: application/json

{
  "message": "Your message",
  "sessionId": "session-id",
  "ragflowConfig": {
    "apiKey": "your-api-key",
    "baseUrl": "http://193.30.121.199",
    "chatId": "2291b3525cfc11f0990e5a471b8c9178"
  }
}
```

## Technical Implementation

### Architecture
- **Client-side**: Configuration management, engine selection, status display
- **Server-side**: Dual engine routing, API integration, error handling
- **Configuration**: Local storage with validation and real-time updates

### Key Files
- `client/src/lib/chatbot-config.ts`: Configuration management utilities
- `client/src/components/webhook-config.tsx`: Settings UI component
- `server/routes.ts`: Server-side engine routing and API integration
- `client/src/lib/chat-api.ts`: Client-side API communication

### Error Handling
- Configuration validation before sending messages
- Fallback error messages for connection issues
- Real-time configuration status updates
- Comprehensive logging for debugging

## Troubleshooting

### Common Issues

1. **"Configuration error" when sending messages**
   - Ensure all required fields are filled in the settings
   - Test the connection before saving
   - Check that the API key and URLs are correct

2. **RAGFlow connection fails**
   - Verify the RAGFlow instance is accessible
   - Check that the API key is valid
   - Ensure the chat ID exists and is accessible

3. **Engine status shows "not configured"**
   - Open settings and complete the configuration
   - Test the connection to verify settings
   - Save the configuration after testing

### Getting Help
- Check the browser console for detailed error messages
- Verify network connectivity to the RAGFlow instance
- Ensure the RAGFlow API is responding correctly

## Future Enhancements

- [ ] **Full Streaming Implementation**: Complete real-time streaming responses
- [ ] **Advanced RAGFlow Features**: Document upload, knowledge base management
- [ ] **Engine-specific Settings**: Per-engine advanced configuration options
- [ ] **Performance Monitoring**: Response time tracking and analytics
- [ ] **Backup Engine**: Automatic fallback between engines
