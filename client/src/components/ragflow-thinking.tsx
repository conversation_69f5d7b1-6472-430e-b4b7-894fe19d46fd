import { useState } from "react";
import { <PERSON>, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface RAGFlowThinkingProps {
  thinking: string;
  className?: string;
  isStreaming?: boolean;
}

export function RAGFlowThinking({ thinking, className = "", isStreaming = false }: RAGFlowThinkingProps) {
  const [isExpanded, setIsExpanded] = useState(isStreaming);

  if (!thinking || thinking.trim() === '') {
    return null;
  }

  return (
    <div className={`mb-3 ${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-between text-xs bg-purple-50 border-purple-200 hover:bg-purple-100 dark:bg-purple-950 dark:border-purple-800 dark:hover:bg-purple-900"
          >
            <div className="flex items-center gap-2">
              <Brain className="h-3 w-3 text-purple-600 dark:text-purple-400" />
              <span className="text-purple-700 dark:text-purple-300">
                {isStreaming ? 'Thinking...' : 'View thinking process'}
              </span>
            </div>
            {isExpanded ? (
              <ChevronUp className="h-3 w-3 text-purple-600 dark:text-purple-400" />
            ) : (
              <ChevronDown className="h-3 w-3 text-purple-600 dark:text-purple-400" />
            )}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="mt-2">
          <Card className="border-l-4 border-l-purple-500 bg-purple-50/50 dark:bg-purple-950/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2 text-purple-700 dark:text-purple-300">
                <Brain className="h-4 w-4" />
                Reasoning Process
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-sm text-purple-800 dark:text-purple-200 whitespace-pre-wrap font-mono bg-purple-100/50 dark:bg-purple-900/50 p-3 rounded border">
                {thinking}
                {isStreaming && (
                  <span className="inline-block w-2 h-4 bg-purple-600 dark:bg-purple-400 ml-1 animate-pulse" />
                )}
              </div>
            </CardContent>
          </Card>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
