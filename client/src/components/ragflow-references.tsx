import { useState } from "react";
import { Ch<PERSON>ronDown, ChevronUp, FileText, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

interface ReferenceChunk {
  id: string;
  content: string;
  document_id: string;
  document_name: string;
  dataset_id: string;
  similarity: number;
  vector_similarity: number;
  term_similarity: number;
  positions?: string[];
}

interface ReferenceData {
  total: number;
  chunks: ReferenceChunk[];
  doc_aggs?: any[];
}

interface RAGFlowReferencesProps {
  reference: ReferenceData;
  className?: string;
}

export function RAGFlowReferences({ reference, className = "" }: RAGFlowReferencesProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!reference || !reference.chunks || reference.chunks.length === 0) {
    return null;
  }

  const { chunks, total } = reference;

  return (
    <div className={`mt-3 ${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-between text-xs"
          >
            <div className="flex items-center gap-2">
              <FileText className="h-3 w-3" />
              <span>
                {total} source{total !== 1 ? 's' : ''} referenced
              </span>
            </div>
            {isExpanded ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <ChevronDown className="h-3 w-3" />
            )}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="mt-2">
          <div className="space-y-2">
            {chunks.map((chunk, index) => (
              <Card key={chunk.id || index} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-500" />
                      <span className="truncate">{chunk.document_name}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {Math.round(chunk.similarity * 100)}% match
                      </Badge>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-sm text-muted-foreground mb-2">
                    <p className="line-clamp-3">{chunk.content}</p>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center gap-3">
                      <span>Vector: {Math.round(chunk.vector_similarity * 100)}%</span>
                      <span>Term: {Math.round(chunk.term_similarity * 100)}%</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={() => {
                        // TODO: Implement document viewing/linking if needed
                        console.log('View document:', chunk.document_id);
                      }}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
