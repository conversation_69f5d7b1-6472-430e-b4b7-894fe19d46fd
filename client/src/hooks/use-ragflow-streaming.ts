import { useState, useCallback } from "react";
import { chatApi } from "@/lib/chat-api";
import { getChatbotConfig } from "@/lib/chatbot-config";

interface StreamingData {
  content?: string;
  reference?: any;
  thinking?: string;
  session_id?: string;
  id?: string;
}

interface UseRAGFlowStreamingProps {
  onThinkingUpdate?: (thinking: string) => void;
  onContentUpdate?: (content: string) => void;
  onReferenceUpdate?: (reference: any) => void;
  onComplete?: (finalData: { content: string; reference?: any; thinking?: string }) => void;
  onError?: (error: string) => void;
}

export function useRAGFlowStreaming({
  onThinkingUpdate,
  onContentUpdate,
  onReferenceUpdate,
  onComplete,
  onError,
}: UseRAGFlowStreamingProps = {}) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState("");
  const [streamingThinking, setStreamingThinking] = useState("");
  const [streamingReference, setStreamingReference] = useState<any>(null);

  const startStreaming = useCallback(async (sessionId: string, message: string) => {
    const config = getChatbotConfig();
    
    if (config.engine !== 'ragflow') {
      onError?.('Streaming is only available for RAGFlow engine');
      return;
    }

    setIsStreaming(true);
    setStreamingContent("");
    setStreamingThinking("");
    setStreamingReference(null);

    let accumulatedContent = "";
    let latestThinking = "";
    let latestReference: any = null;

    try {
      await chatApi.streamRAGFlowMessage(
        sessionId,
        message,
        (data: StreamingData) => {
          // Handle content updates
          if (data.content !== undefined) {
            accumulatedContent += data.content;
            setStreamingContent(accumulatedContent);
            onContentUpdate?.(accumulatedContent);
          }

          // Handle thinking process updates
          if (data.thinking !== undefined && data.thinking !== latestThinking) {
            latestThinking = data.thinking;
            setStreamingThinking(latestThinking);
            onThinkingUpdate?.(latestThinking);
          }

          // Handle reference updates
          if (data.reference && Object.keys(data.reference).length > 0) {
            latestReference = data.reference;
            setStreamingReference(latestReference);
            onReferenceUpdate?.(latestReference);
          }
        },
        (error: string) => {
          console.error('RAGFlow streaming error:', error);
          setIsStreaming(false);
          onError?.(error);
        },
        () => {
          setIsStreaming(false);
          onComplete?.({
            content: accumulatedContent,
            reference: latestReference,
            thinking: latestThinking,
          });
        }
      );
    } catch (error) {
      console.error('Error starting RAGFlow stream:', error);
      setIsStreaming(false);
      onError?.(error instanceof Error ? error.message : 'Unknown streaming error');
    }
  }, [onThinkingUpdate, onContentUpdate, onReferenceUpdate, onComplete, onError]);

  const stopStreaming = useCallback(() => {
    setIsStreaming(false);
    // Note: We can't actually stop the server-side stream, but we can stop processing it
  }, []);

  return {
    isStreaming,
    streamingContent,
    streamingThinking,
    streamingReference,
    startStreaming,
    stopStreaming,
  };
}
